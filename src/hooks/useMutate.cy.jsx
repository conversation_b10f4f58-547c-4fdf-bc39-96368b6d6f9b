import React from 'react';
import useMutate from './useMutate';

// Test component that uses the useMutate hook
const TestComponent = ({ url, method }) => {
  const { trigger, inProgress, error } = useMutate(url, method);
  const [body, setBody] = React.useState('');

  return (
    <div>
      <button
        onClick={() =>
          trigger(
            { test: 'data' },
            { headers: { 'custom-header': 'custom-value' } },
            {
              onSuccess: (data, _key, _config) => {
                setBody(data?.boo);
              },
            },
          )
        }
        data-testid="trigger-button"
      >
        Trigger Mutation
      </button>
      {inProgress && <div data-testid="loading">Loading...</div>}
      {error && <div data-testid="error">Error: {error.message}</div>}
      <div data-testid="success">Success</div>
      <div data-testid="body">{body}</div>
    </div>
  );
};

const testMutation = (method, expectBody = true) => {
  const url = 'https://api.example.com/data';

  cy.intercept(method, url, {
    statusCode: 200,
    body: { success: true, boo: 'BOO!' },
    delay: 50,
  }).as('apiCall');

  cy.mountAuthorized(<TestComponent url={url} method={method} />);

  cy.get('[data-testid="loading"]').should('not.exist');
  cy.get('[data-testid="trigger-button"]').click();
  cy.get('[data-testid="loading"]').should('be.visible');
  cy.wait('@apiCall').then(interception => {
    expect(interception.request.headers['authorization']).to.equal('Bearer mock-token');
    expect(interception.request.headers['content-type']).to.equal('application/json');
    expect(interception.request.headers['custom-header']).to.equal('custom-value');
    expect(interception.request.method).to.equal(method);
    if (expectBody) {
      expect(interception.request.body).to.deep.equal({ test: 'data' });
    } else {
      expect(interception.request.body).to.equal('');
    }
  });
  cy.get('[data-testid="success"]').should('be.visible');
  cy.get('[data-testid="body"]').should('contain', 'BOO!');
};

describe('useMutate Hook', () => {
  it('should trigger POST mutation and handle success', () => {
    testMutation('POST');
  });

  it('should trigger GET mutation and handle success', () => {
    testMutation('GET', false);
  });

  it('should trigger PUT mutation and handle success', () => {
    testMutation('PUT');
  });

  it('should trigger PATCH mutation and handle success', () => {
    testMutation('PATCH');
  });

  it('should trigger DELETE mutation and handle success', () => {
    testMutation('DELETE', false);
  });

  it('should handle network failures', () => {
    const url = 'https://api.example.com/data';
    const method = 'POST';

    cy.on('uncaught:exception', () => false);

    cy.intercept('POST', url, { statusCode: 500, body: { message: 'Server Error' } }).as(
      'networkError',
    );

    cy.mountAuthorized(<TestComponent url={url} method={method} />);

    cy.get('[data-testid="trigger-button"]').click();
    cy.wait('@networkError');
    cy.get('[data-testid="error"]', { timeout: 10000 }).should('be.visible');
    cy.get('[data-testid="error"]').should('contain', 'Network response was not ok');
  });
});

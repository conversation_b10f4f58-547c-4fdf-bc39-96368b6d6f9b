import { useState, useEffect } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { jwtDecode } from 'jwt-decode';

const usePermissionCheck = (requiredPermission, audience) => {
  const { getAccessTokenSilently } = useAuth0();
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    const fetchTokenAndCheckPermission = async () => {
      try {
        const tokenOptions = audience ? { audience: audience } : {};
        const token = await getAccessTokenSilently(tokenOptions);
        const decoded = jwtDecode(token);
        const permissions = decoded.permissions || [];
        setHasPermission(permissions.includes(requiredPermission));
      } catch (error) {
        console.error('Error fetching access token', error);
      }
    };

    fetchTokenAndCheckPermission();
  }, [getAccessTokenSilently, requiredPermission, audience]);

  return hasPermission;
};

export default usePermissionCheck;

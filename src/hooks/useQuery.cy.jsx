import React from 'react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { mount } from 'cypress/react';
import useQuery from './useQuery';

const TestComponent = () => {
  const query = useQuery();

  return (
    <div>
      <div data-testid="param1">{query.get('param1')}</div>
      <div data-testid="param2">{query.get('param2')}</div>
    </div>
  );
};

describe('useQuery Hook', () => {
  it('should parse query parameters correctly', () => {
    mount(
      <MemoryRouter initialEntries={['/test?param1=value1&param2=value2']}>
        <Routes>
          <Route path="*" element={<TestComponent />} />
        </Routes>
      </MemoryRouter>,
    );

    cy.get('[data-testid="param1"]').should('have.text', 'value1');
    cy.get('[data-testid="param2"]').should('have.text', 'value2');
  });

  it('should return null for non-existent query parameters', () => {
    mount(
      <MemoryRouter initialEntries={['/test?param1=value1']}>
        <Routes>
          <Route path="*" element={<TestComponent />} />
        </Routes>
      </MemoryRouter>,
    );

    cy.get('[data-testid="param1"]').should('have.text', 'value1');
    cy.get('[data-testid="param2"]').should('be.empty');
  });
});

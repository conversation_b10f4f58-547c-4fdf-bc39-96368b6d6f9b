import React from 'react';
import Login from './Login';

describe('Login Component', () => {
  beforeEach(() => {
    cy.mount(<Login />);
  });

  it('renders a loading indicator', () => {
    cy.get('circle').should('exist');
  });

  it('redirects to auth0 on mount', () => {
    cy.intercept('GET', 'https://*.auth0.com/authorize*', { statusCode: 200 }).as('loginRedirect');
    cy.intercept('GET', 'https://*.auth0.com*setLocalStorage*', { statusCode: 200 });

    const redirectUri = `${Cypress.config().baseUrl}/auth/auth0/callback`;

    cy.wait('@loginRedirect')
      .its('request.query')
      .then(query => {
        expect(query.audience).to.eq(Cypress.env('VITE_INSIGHTS_APP_API_AUTH0_AUDIENCE'));
        expect(query.redirect_uri).to.eq(redirectUri);
        expect(query.scope).to.eq(
          'openid profile email read:me read:admin_settings read:insights update:insights read:user_interactions update:user_interactions read:revenue_forecasts read:competitors_agent_reports',
        );
      });
  });
});

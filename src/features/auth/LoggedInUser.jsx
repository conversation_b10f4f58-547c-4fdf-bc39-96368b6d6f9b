import React, { useContext } from 'react';
import { Box, IconButton, Typography } from '@mui/material';
import { State } from '../../State';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import { useNavigate } from 'react-router-dom';

const LoggedInUser = () => {
  const navigate = useNavigate();
  const { state } = useContext(State);

  const handleLoginNavigation = () => {
    navigate('/login');
  };

  const loginDetailsSx = {
    margin: '20px',
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
  };
  const orgDisplayNameSx = {
    fontWeight: '500',
    fontSize: '20px',
    letterSpacing: '0.15px',
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
  };
  const userDetailSx = { fontSize: '11px', color: 'gray', gap: '5px' };

  return (
    <Box sx={loginDetailsSx}>
      <Box sx={orgDisplayNameSx}>
        <Typography variant="h6">{state.displayName}</Typography>
        <IconButton onClick={handleLoginNavigation}>
          <SwapHorizIcon />
        </IconButton>
      </Box>
      <Typography sx={userDetailSx}>{state.email}</Typography>
      {import.meta.env.MODE !== 'production' && (
        <Typography sx={userDetailSx}>{state.connection}</Typography>
      )}
    </Box>
  );
};

export default LoggedInUser;

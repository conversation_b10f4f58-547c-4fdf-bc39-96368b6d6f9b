import "./App.css";
import { BrowserRouter } from "react-router-dom";
import { ThemeProvider } from "@mui/material";

import theme from "../theme/theme";
import Auth0ProviderWithHistory from "./providers/Auth0ProviderWithHistory";
import StateProvider from "./providers/State";
import SWRProvider from "./providers/SWRProvider";

function App({ children }) {
  return (
    <StateProvider>
      <ThemeProvider theme={theme}>
        <BrowserRouter>
          <Auth0ProviderWithHistory
            audience={import.meta.env.VITE_INSIGHTS_APP_API_AUTH0_AUDIENCE}
            domain={import.meta.env.VITE_AUTH0_DOMAIN}
            clientId={import.meta.env.VITE_AUTH0_CLIENT_ID}
          >
            <SWRProvider>{children}</SWRProvider>
          </Auth0ProviderWithHistory>
        </BrowserRouter>
      </ThemeProvider>
    </StateProvider>
  );
}

export default App;

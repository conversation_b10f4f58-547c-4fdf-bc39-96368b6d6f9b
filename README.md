# insights-app-fe-core

This repository contains the core foundation for the Insights App Frontend, built using a Micro Frontend architecture pattern. It serves as the shared foundation package that other micro frontend applications depend on.

## Overview

The `insights-app-fe-core` is a centralized npm package that provides essential shared functionality and components for all micro frontends within the Insights App ecosystem, as well as for the Insights App container app itself. This approach ensures consistency, reduces code duplication, and maintains a unified user experience across different application modules.

## Features

### Authentication System

- Centralized authentication logic and state management
- Token management and refresh mechanisms
- Route protection and authorization utilities
- Login/logout workflows

### Global State Management

- Shared application state across micro frontends
- User session and preferences management

### Theme System

- Consistent design system and styling
- Customizable component themes
- CSS-in-JS or CSS variables for theming

### Core Components

- **Authentication Components**: Login forms, protected routes, user profile widgets
- **Notification System**: Toast notifications, alert banners, status messages

## Installation

```bash
npm install @baresquare/insights-app-fe-core
```

## Usage

This is a placeholder for the actual usage documentation. The interface is not finalized yet.

```javascript
import {
  AuthProvider,
  ThemeProvider,
  NotificationProvider,
} from "insights-app-fe-core";
import {
  LoginComponent,
  ProtectedRoute,
} from "insights-app-fe-core/components";
```

## Dependencies

- React
- React Router
- Material UI
- Authentication library (Auth0)
- Data fetching and caching management (SWR)

## Development

```bash
# Install dependencies
npm install

# Build the package
npm run build

# Run tests
npm run test

# Publish to npm
npm publish
```

## Integration

### To the container app

The Insights App container app should import and use this core package as its foundation layer before implementing container-specific features.

### To other micro frontend apps

Other micro frontend applications should import and use this core package for theis standalone versions, but only export the necessary components and utilities to the container app.

## Contributing

Please follow the established patterns and ensure all changes maintain backward compatibility, as this package affects multiple consuming applications.
